<?php
/**
 * Tutor Instructor Sistemini Test Et
 *
 * Bu dosya tutor instructor kull<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> için eğitmen sisteminin
 * doğru çalışıp çalışmadığını test eder.
 *
 * Kullanım: WordPress root dizininde bu dosyayı çalıştırın
 * Örnek: php test-tutor-instructor.php
 */

// WordPress'i yükle
if (file_exists('./wp-config.php')) {
    require_once('./wp-config.php');
    require_once('./wp-load.php');
} else {
    die("WordPress bulunamadı! Bu dosyayı WordPress root dizininde çalıştırın.\n");
}

echo "=== Tutor Instructor Sistemi Test ===\n\n";

// 1. Tutor LMS aktif mi kontrol et
if (!class_exists('TUTOR\Tutor') || !function_exists('tutor_time')) {
    die("✗ Tutor LMS bulunamadı! Önce Tutor LMS eklentisini etkinleştirin.\n");
}

echo "✓ Tutor LMS aktif.\n";

// 2. Test kullanıcısı oluştur
$test_username = 'test_dynamic_instructor_' . time();
$test_email = $test_username . '@test.com';

echo "📝 Test kullanıcısı oluşturuluyor: {$test_username}\n";

$test_user_id = wp_create_user($test_username, 'test123', $test_email);

if (is_wp_error($test_user_id)) {
    die("✗ Test kullanıcısı oluşturulamadı: " . $test_user_id->get_error_message() . "\n");
}

echo "✓ Test kullanıcısı oluşturuldu (ID: {$test_user_id})\n\n";

// 3. İlk durum: Normal kullanıcı (eğitmen değil)
echo "=== Test 1: Normal Kullanıcı (Eğitmen Değil) ===\n";
test_instructor_status($test_user_id, "Normal kullanıcı");

// 4. Tutor Instructor rolü ekle
echo "\n=== Test 2: Tutor Instructor Rolü Ekleme ===\n";
$user = new WP_User($test_user_id);
$user->add_role('tutor_instructor');
echo "✓ Superole rolü eklendi\n";

// Cache'i temizle
wp_cache_flush();

test_instructor_status($test_user_id, "Superole kullanıcısı");

// 5. Superole rolünü kaldır
echo "\n=== Test 3: Superole Rolünü Kaldırma ===\n";
$user->remove_role('superole');
echo "✓ Superole rolü kaldırıldı\n";

// Cache'i temizle
wp_cache_flush();

test_instructor_status($test_user_id, "Superole rolü kaldırıldıktan sonra");

// 6. Gerçek tutor_instructor rolü ekle
echo "\n=== Test 4: Gerçek Tutor Instructor Rolü ===\n";
$user->add_role('tutor_instructor');
update_user_meta($test_user_id, '_is_tutor_instructor', tutor_time());
update_user_meta($test_user_id, '_tutor_instructor_status', 'approved');
echo "✓ Gerçek tutor_instructor rolü ve meta verileri eklendi\n";

// Cache'i temizle
wp_cache_flush();

test_instructor_status($test_user_id, "Gerçek eğitmen");

// 7. Superole rolü tekrar ekle (gerçek eğitmen + superole)
echo "\n=== Test 5: Gerçek Eğitmen + Superole ===\n";
$user->add_role('superole');
echo "✓ Superole rolü tekrar eklendi (gerçek eğitmen + superole)\n";

// Cache'i temizle
wp_cache_flush();

test_instructor_status($test_user_id, "Gerçek eğitmen + superole");

// 8. Superole rolünü kaldır (gerçek eğitmen kalmalı)
echo "\n=== Test 6: Superole Kaldır (Gerçek Eğitmen Korunmalı) ===\n";
$user->remove_role('superole');
echo "✓ Superole rolü kaldırıldı (gerçek eğitmen rolü korunmalı)\n";

// Cache'i temizle
wp_cache_flush();

test_instructor_status($test_user_id, "Superole kaldırıldıktan sonra gerçek eğitmen");

// 9. Test kullanıcısını temizle
echo "\n=== Temizlik ===\n";
wp_delete_user($test_user_id);
echo "✓ Test kullanıcısı silindi\n";

echo "\n=== Test Tamamlandı ===\n";

/**
 * Kullanıcının eğitmen durumunu test et
 */
function test_instructor_status($user_id, $test_name) {
    echo "🧪 Test: {$test_name}\n";
    
    $user = get_userdata($user_id);
    echo "   Roller: " . implode(', ', $user->roles) . "\n";
    
    // Meta verileri kontrol et
    $is_instructor_meta = get_user_meta($user_id, '_is_tutor_instructor', true);
    $instructor_status = get_user_meta($user_id, '_tutor_instructor_status', true);
    
    echo "   Meta veriler:\n";
    echo "   - _is_tutor_instructor: " . ($is_instructor_meta ? "✓ Var" : "✗ Yok") . "\n";
    echo "   - _tutor_instructor_status: " . ($instructor_status ? "✓ {$instructor_status}" : "✗ Yok") . "\n";
    
    // Tutor LMS fonksiyonlarını test et
    echo "   Tutor LMS testleri:\n";
    
    // User::is_instructor() testi
    if (class_exists('TUTOR\User')) {
        $is_instructor_func = TUTOR\User::is_instructor($user_id);
        echo "   - User::is_instructor(): " . ($is_instructor_func ? "✓ TRUE" : "✗ FALSE") . "\n";
    }
    
    // tutor_utils()->is_instructor() testi
    if (function_exists('tutor_utils')) {
        $is_instructor_utils = tutor_utils()->is_instructor($user_id);
        echo "   - tutor_utils()->is_instructor(): " . ($is_instructor_utils ? "✓ TRUE" : "✗ FALSE") . "\n";
    }
    
    // Capability testi
    $old_user = wp_get_current_user();
    wp_set_current_user($user_id);
    $can_instructor = current_user_can('tutor_instructor');
    wp_set_current_user($old_user->ID);
    
    echo "   - current_user_can('tutor_instructor'): " . ($can_instructor ? "✓ TRUE" : "✗ FALSE") . "\n";
    
    // Course oluşturma yetki kontrolü
    $has_access = false;
    if (class_exists('TUTOR\User')) {
        $has_access = TUTOR\User::is_admin($user_id) || TUTOR\User::is_instructor($user_id);
    }
    echo "   - Course AJAX Access: " . ($has_access ? "✓ TRUE" : "✗ FALSE") . "\n";
    
    // Beklenen sonuç
    $has_superole = in_array('superole', $user->roles);
    $has_real_instructor = in_array('tutor_instructor', $user->roles);
    
    $should_be_instructor = $has_superole || $has_real_instructor;
    
    echo "   Beklenen: " . ($should_be_instructor ? "Eğitmen olmalı" : "Eğitmen olmamalı") . "\n";
    echo "   Sonuç: " . ($has_access ? "✅ BAŞARILI" : "❌ BAŞARISIZ") . "\n";
}
?>
