<?php
/**
 * Role Custom Debug - Rol Kontrol Dosyası
 *
 * Bu dosyayı WordPress root dizininde çalıştırarak Tutor Instructor rolünün
 * durumunu kontrol edebilirsiniz.
 *
 * Kullanım: WordPress root dizininde bu dosyayı çalıştırın
 * Örnek: php debug-role-check.php
 */

// WordPress'i yükle
if (file_exists('./wp-config.php')) {
    require_once('./wp-config.php');
    require_once('./wp-load.php');
} else {
    die("WordPress bulunamadı! Bu dosyayı WordPress root dizininde çalıştırın.\n");
}

echo "=== Role Custom Debug - Rol Kontrol ===\n\n";

// 1. Eklenti aktif mi kontrol et
$active_plugins = get_option('active_plugins', []);
$plugin_active = false;
foreach ($active_plugins as $plugin) {
    if (strpos($plugin, 'role-custom') !== false) {
        $plugin_active = true;
        break;
    }
}

echo "1. Eklenti Durumu: " . ($plugin_active ? "✓ Aktif" : "✗ Aktif değil") . "\n";

// 2. Role Custom sınıfı yüklü mü?
$class_exists = class_exists('Role_Custom');
echo "2. Role_Custom Sınıfı: " . ($class_exists ? "✓ Yüklü" : "✗ Yüklü değil") . "\n";

// 3. Tutor Instructor rolü var mı?
$instructor_role = get_role('tutor_instructor');
echo "3. Tutor Instructor Rolü: " . ($instructor_role ? "✓ Mevcut" : "✗ Mevcut değil") . "\n";

if ($instructor_role) {
    echo "   - Yetki Sayısı: " . count($instructor_role->capabilities) . "\n";
    echo "   - Temel Yetkiler:\n";
    
    $important_caps = [
        'read' => 'Okuma',
        'manage_tutor' => 'Tutor Yönetimi (Öğrenciler için gerekli)',
        'manage_tutor_instructor' => 'Tutor Eğitmen Yönetimi',
        'manage_woocommerce' => 'WooCommerce Yönetimi',
        'view_woocommerce_reports' => 'WooCommerce Raporları',
        'edit_shop_orders' => 'Sipariş Düzenleme'
    ];
    
    foreach ($important_caps as $cap => $desc) {
        $has_cap = $instructor_role->has_cap($cap);
        echo "     - {$desc}: " . ($has_cap ? "✓" : "✗") . "\n";
    }
}

// 4. Tüm rolleri listele
echo "\n4. Tüm WordPress Rolleri:\n";
$all_roles = wp_roles()->get_names();
foreach ($all_roles as $role_key => $role_name) {
    $marker = ($role_key === 'tutor_instructor') ? " ← TUTOR INSTRUCTOR" : "";
    echo "   - {$role_key}: {$role_name}{$marker}\n";
}

// 5. Tutor Instructor rolüne sahip kullanıcılar
echo "\n5. Tutor Instructor Rolüne Sahip Kullanıcılar:\n";
$instructor_users = get_users(['role' => 'tutor_instructor']);
if (empty($instructor_users)) {
    echo "   - Henüz hiçbir kullanıcı Tutor Instructor rolüne sahip değil.\n";
} else {
    foreach ($instructor_users as $user) {
        echo "   - {$user->display_name} ({$user->user_login})\n";

        // Tutor LMS eğitmen meta verilerini kontrol et
        if (function_exists('tutor_time')) {
            $is_instructor = get_user_meta($user->ID, '_is_tutor_instructor', true);
            $instructor_status = get_user_meta($user->ID, '_tutor_instructor_status', true);
            $instructor_approved = get_user_meta($user->ID, '_tutor_instructor_approved', true);

            echo "     Tutor LMS Eğitmen Durumu:\n";
            echo "     - _is_tutor_instructor: " . ($is_instructor ? "✓ Var (" . date('Y-m-d H:i:s', $is_instructor) . ")" : "✗ Yok") . "\n";
            echo "     - _tutor_instructor_status: " . ($instructor_status ? "✓ {$instructor_status}" : "✗ Yok") . "\n";
            echo "     - _tutor_instructor_approved: " . ($instructor_approved ? "✓ Var (" . date('Y-m-d H:i:s', $instructor_approved) . ")" : "✗ Yok") . "\n";

            // Tutor instructor rolü kontrolü
            $has_tutor_role = $user->has_cap('tutor_instructor');
            $has_tutor_role_direct = in_array('tutor_instructor', $user->roles);
            echo "     - tutor_instructor capability: " . ($has_tutor_role ? "✓ Var" : "✗ Yok") . "\n";
            echo "     - tutor_instructor rolü: " . ($has_tutor_role_direct ? "✓ Var" : "✗ Yok") . "\n";

            // current_user_can kontrolü simülasyonu
            if (function_exists('tutor') && isset(tutor()->instructor_role)) {
                $instructor_role = tutor()->instructor_role;
                $can_instructor = $user->has_cap($instructor_role);
                echo "     - current_user_can('{$instructor_role}'): " . ($can_instructor ? "✓ Geçer" : "✗ Geçmez") . "\n";
            }
        } else {
            echo "     - Tutor LMS bulunamadı\n";
        }
    }
}

// 6. Tutor Instructor rolü WooCommerce yetkileri kontrolü
echo "\n6. Tutor Instructor WooCommerce Yetkileri Kontrolü:\n";
if ($instructor_role) {
    echo "   Tutor Instructor rolü bulundu, WooCommerce yetkilerini kontrol ediliyor...\n";

    // WooCommerce yetkileri
    $woocommerce_capabilities = [
        'manage_woocommerce' => 'WooCommerce Yönetimi',
        'view_woocommerce_reports' => 'WooCommerce Raporları',
        'edit_shop_orders' => 'Sipariş Düzenleme',
        'edit_others_shop_orders' => 'Diğer Siparişleri Düzenleme',
        'edit_products' => 'Ürün Düzenleme',
        'edit_others_products' => 'Diğer Ürünleri Düzenleme',
    ];

    foreach ($woocommerce_capabilities as $cap => $desc) {
        $has_cap = $instructor_role->has_cap($cap);
        echo "   - {$desc}: " . ($has_cap ? "✓ Var" : "✗ Yok") . "\n";
    }
} else {
    echo "   ✗ Tutor Instructor rolü bulunamadı!\n";
}

// 7. WordPress veritabanı kontrol
echo "\n7. Veritabanı Kontrol:\n";
global $wpdb;
$roles_option = get_option($wpdb->prefix . 'user_roles');
if ($roles_option && isset($roles_option['tutor_instructor'])) {
    echo "   ✓ Tutor Instructor rolü veritabanında mevcut.\n";
    echo "   - Veritabanındaki yetki sayısı: " . count($roles_option['tutor_instructor']['capabilities']) . "\n";
} else {
    echo "   ✗ Tutor Instructor rolü veritabanında bulunamadı.\n";
    echo "   - Mevcut roller: " . implode(', ', array_keys($roles_option ?: [])) . "\n";
}

// 8. Tutor LMS Eğitmen Kurulum Durumu
echo "\n8. Tutor LMS Eğitmen Kurulum Durumu:\n";
if (function_exists('tutor_time')) {
    echo "   ✓ Tutor LMS aktif.\n";

    $setup_done = get_option('role_custom_superole_instructor_setup_done', false);
    echo "   - Eğitmen kurulum tamamlandı mı: " . ($setup_done ? "✓ Evet" : "✗ Hayır") . "\n";

    if (!empty($superole_users)) {
        $instructor_count = 0;
        $tutor_role_count = 0;
        foreach ($superole_users as $user) {
            if (get_user_meta($user->ID, '_is_tutor_instructor', true)) {
                $instructor_count++;
            }
            if (in_array('tutor_instructor', $user->roles)) {
                $tutor_role_count++;
            }
        }
        echo "   - Eğitmen meta verilerine sahip superole kullanıcı sayısı: {$instructor_count}/" . count($superole_users) . "\n";
        echo "   - tutor_instructor rolüne sahip superole kullanıcı sayısı: {$tutor_role_count}/" . count($superole_users) . "\n";

        // Eksik olanları düzelt
        if ($instructor_count < count($superole_users) || $tutor_role_count < count($superole_users)) {
            echo "   ⚠️  Bazı superole kullanıcılarında eksiklik tespit edildi!\n";
            echo "   💡 Çözüm: Role Custom eklentisini devre dışı bırakıp tekrar etkinleştirin.\n";
        }
    }
} else {
    echo "   ✗ Tutor LMS bulunamadı.\n";
}

// 10. Tutor LMS Menü Kontrolü
echo "\n10. Tutor LMS Menü Durumu:\n";
if (class_exists('TUTOR\Admin')) {
    echo "   ✓ Tutor LMS Admin sınıfı mevcut.\n";

    // İzin verilen menüler
    $allowed_menus = [
        'tutor' => 'Kurslar',
        'tutor-students' => 'Öğrenciler',
        'tutor_announcements' => 'Duyurular',
        'question_answer' => 'Q&A',
        'tutor_quiz_attempts' => 'Sınav Denemeleri'
    ];

    echo "   İzin verilen Tutor LMS menüleri:\n";
    foreach ($allowed_menus as $slug => $name) {
        echo "     - {$name} ({$slug})\n";
    }

    // Öğrenciler menüsü için özel kontrol
    if ($superole_role && $superole_role->has_cap('manage_tutor')) {
        echo "   ✓ Superole rolü 'manage_tutor' yetkisine sahip (Öğrenciler menüsü için gerekli).\n";
    } else {
        echo "   ✗ Superole rolü 'manage_tutor' yetkisine sahip değil!\n";
    }
} else {
    echo "   ✗ Tutor LMS bulunamadı.\n";
}

// 11. WooCommerce Menü Durumu
echo "\n11. WooCommerce Menü Durumu:\n";
if (class_exists('WooCommerce')) {
    echo "   ✓ WooCommerce aktif.\n";

    // İzin verilen WooCommerce menüleri
    echo "   İzin verilen WooCommerce menüleri:\n";
    echo "     - WooCommerce (ana sayfa)\n";
    echo "     - Ürünler\n";
    echo "     - Analiz\n";
    echo "     - Pazarlama\n";

    // Gizlenen menüler
    echo "   Gizlenen WooCommerce menüleri:\n";
    echo "     - Ödemeler (ana sekme)\n";
    echo "     - Ayarlar (alt menü)\n";
    echo "     - Durum (alt menü)\n";
    echo "     - Genişletme Paketleri (alt menü)\n";

} else {
    echo "   ✗ WooCommerce bulunamadı.\n";
}

// 12. Öneriler
echo "\n12. Sorun Giderme Önerileri:\n";
if (!$plugin_active) {
    echo "   - Role Custom eklentisini WordPress admin panelinden etkinleştirin\n";
}
if (!$superole_role) {
    echo "   - Eklentiyi devre dışı bırakıp tekrar etkinleştirin\n";
    echo "   - WordPress admin panelinde Kullanıcılar > Tüm Kullanıcılar sayfasını yenileyin\n";
    echo "   - Tarayıcı cache'ini temizleyin\n";
}
if ($superole_role && !$superole_role->has_cap('manage_tutor')) {
    echo "   - Eklentiyi devre dışı bırakıp tekrar etkinleştirin (manage_tutor yetkisi eksik)\n";
}

echo "\n=== Debug Tamamlandı ===\n";
?>
