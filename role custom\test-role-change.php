<?php
/**
 * <PERSON><PERSON> Değişikliği Real-time Test
 *
 * <PERSON><PERSON> dosya tutor_instructor -> abone rol değişikliğini gerçek zamanlı test eder.
 *
 * Kullanım: WordPress root dizininde bu dosyayı çalıştırın
 * Örnek: php test-role-change.php
 */

// WordPress'i yükle
if (file_exists('./wp-config.php')) {
    require_once('./wp-config.php');
    require_once('./wp-load.php');
} else {
    die("WordPress bulunamadı! Bu dosyayı WordPress root dizininde çalıştırın.\n");
}

echo "=== Rol Değişikliği Real-time Test ===\n\n";

// 1. Tutor LMS aktif mi kontrol et
if (!class_exists('TUTOR\Tutor') || !function_exists('tutor_time')) {
    die("✗ Tutor LMS bulunamadı! Önce Tutor LMS eklentisini etkinleştirin.\n");
}

echo "✓ Tutor LMS aktif.\n";

// 2. Test kullanıcısı oluştur
$test_username = 'test_role_change_' . time();
$test_email = $test_username . '@test.com';

echo "📝 Test kullanıcısı oluşturuluyor: {$test_username}\n";

$test_user_id = wp_create_user($test_username, 'test123', $test_email);

if (is_wp_error($test_user_id)) {
    die("✗ Test kullanıcısı oluşturulamadı: " . $test_user_id->get_error_message() . "\n");
}

echo "✓ Test kullanıcısı oluşturuldu (ID: {$test_user_id})\n\n";

// 3. Tutor Instructor rolü ver
echo "=== Adım 1: Tutor Instructor Rolü Verme ===\n";
$user = new WP_User($test_user_id);
$user->set_role('tutor_instructor');
echo "✓ Tutor Instructor rolü verildi\n";

// Cache'i temizle
wp_cache_flush();
clean_user_cache($test_user_id);

// Durumu kontrol et
test_instructor_status($test_user_id, "Superole rolü verildikten sonra");

// 4. Abone rolüne geçir
echo "\n=== Adım 2: Abone Rolüne Geçiş ===\n";
$user->set_role('subscriber');
echo "✓ Abone rolüne geçirildi\n";

// Cache'i temizle
wp_cache_flush();
clean_user_cache($test_user_id);

// Durumu kontrol et
test_instructor_status($test_user_id, "Abone rolüne geçtikten sonra");

// 5. Tekrar superole yap
echo "\n=== Adım 3: Tekrar Superole Yapma ===\n";
$user->set_role('superole');
echo "✓ Tekrar superole rolü verildi\n";

// Cache'i temizle
wp_cache_flush();
clean_user_cache($test_user_id);

// Durumu kontrol et
test_instructor_status($test_user_id, "Tekrar superole olduktan sonra");

// 6. Admin paneli simülasyonu - add_role/remove_role
echo "\n=== Adım 4: Admin Paneli Simülasyonu ===\n";
$user->remove_role('superole');
$user->add_role('subscriber');
echo "✓ Admin paneli tarzı rol değişikliği yapıldı (remove + add)\n";

// Cache'i temizle
wp_cache_flush();
clean_user_cache($test_user_id);

// Durumu kontrol et
test_instructor_status($test_user_id, "Admin paneli tarzı rol değişikliği sonrası");

// 7. Global fonksiyon testi
echo "\n=== Adım 5: Global Fonksiyon Testi ===\n";
$user->set_role('tutor_instructor');
update_user_meta($test_user_id, '_is_tutor_instructor', tutor_time());
update_user_meta($test_user_id, '_tutor_instructor_status', 'approved');
echo "✓ Gerçek eğitmen rolü ve meta verileri eklendi\n";

// Global fonksiyonu test et
if (function_exists('role_custom_clean_instructor_meta_data')) {
    $result = role_custom_clean_instructor_meta_data($test_user_id);
    echo "✓ Global temizleme fonksiyonu çalıştırıldı: " . ($result ? "Başarılı" : "Başarısız") . "\n";
} else {
    echo "✗ Global temizleme fonksiyonu bulunamadı\n";
}

// Cache'i temizle
wp_cache_flush();
clean_user_cache($test_user_id);

// Durumu kontrol et
test_instructor_status($test_user_id, "Global fonksiyon ile temizleme sonrası");

// 8. Test kullanıcısını temizle
echo "\n=== Temizlik ===\n";
wp_delete_user($test_user_id);
echo "✓ Test kullanıcısı silindi\n";

echo "\n=== Test Tamamlandı ===\n";

/**
 * Kullanıcının eğitmen durumunu test et
 */
function test_instructor_status($user_id, $test_name) {
    echo "🧪 Test: {$test_name}\n";
    
    // Kullanıcı verilerini yeniden yükle
    $user = get_userdata($user_id);
    if (!$user) {
        echo "   ❌ Kullanıcı bulunamadı!\n";
        return;
    }
    
    echo "   Roller: " . implode(', ', $user->roles) . "\n";
    
    // Meta verileri kontrol et
    $is_instructor_meta = get_user_meta($user_id, '_is_tutor_instructor', true);
    $instructor_status = get_user_meta($user_id, '_tutor_instructor_status', true);
    
    echo "   Meta veriler:\n";
    echo "   - _is_tutor_instructor: " . ($is_instructor_meta ? "✓ Var (" . date('Y-m-d H:i:s', $is_instructor_meta) . ")" : "✗ Yok") . "\n";
    echo "   - _tutor_instructor_status: " . ($instructor_status ? "✓ {$instructor_status}" : "✗ Yok") . "\n";
    
    // Tutor LMS fonksiyonlarını test et
    echo "   Tutor LMS testleri:\n";
    
    // User::is_instructor() testi
    if (class_exists('TUTOR\User')) {
        $is_instructor_func = TUTOR\User::is_instructor($user_id);
        echo "   - User::is_instructor(): " . ($is_instructor_func ? "✓ TRUE" : "✗ FALSE") . "\n";
    }
    
    // tutor_utils()->is_instructor() testi
    if (function_exists('tutor_utils')) {
        $is_instructor_utils = tutor_utils()->is_instructor($user_id);
        echo "   - tutor_utils()->is_instructor(): " . ($is_instructor_utils ? "✓ TRUE" : "✗ FALSE") . "\n";
    }
    
    // Capability testi
    $old_user = wp_get_current_user();
    wp_set_current_user($user_id);
    $can_instructor = current_user_can('tutor_instructor');
    wp_set_current_user($old_user->ID);
    
    echo "   - current_user_can('tutor_instructor'): " . ($can_instructor ? "✓ TRUE" : "✗ FALSE") . "\n";
    
    // Course oluşturma yetki kontrolü
    $has_access = false;
    if (class_exists('TUTOR\User')) {
        $has_access = TUTOR\User::is_admin($user_id) || TUTOR\User::is_instructor($user_id);
    }
    echo "   - Course AJAX Access: " . ($has_access ? "✓ TRUE" : "✗ FALSE") . "\n";
    
    // Beklenen sonuç
    $has_superole = in_array('superole', $user->roles);
    $has_real_instructor = in_array('tutor_instructor', $user->roles);
    
    $should_be_instructor = $has_superole || $has_real_instructor;
    
    echo "   Beklenen: " . ($should_be_instructor ? "Eğitmen olmalı" : "Eğitmen olmamalı") . "\n";
    
    if ($should_be_instructor && $has_access) {
        echo "   Sonuç: ✅ BAŞARILI - Eğitmen yetkilerine sahip\n";
    } elseif (!$should_be_instructor && !$has_access) {
        echo "   Sonuç: ✅ BAŞARILI - Eğitmen yetkilerine sahip değil\n";
    } else {
        echo "   Sonuç: ❌ BAŞARISIZ - Beklenen durumla uyuşmuyor\n";
        echo "   Debug: should_be_instructor={$should_be_instructor}, has_access={$has_access}\n";
    }
    
    echo "\n";
}

/**
 * Manuel test için kullanıcı ID'si ile test
 */
function test_existing_user($user_id) {
    echo "=== Mevcut Kullanıcı Test (ID: {$user_id}) ===\n";
    test_instructor_status($user_id, "Mevcut durum");
}

// Eğer komut satırından kullanıcı ID'si verilmişse test et
if (isset($argv[1]) && is_numeric($argv[1])) {
    echo "\n";
    test_existing_user($argv[1]);
}
?>
